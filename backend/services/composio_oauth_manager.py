"""
Composio OAuth Connection Manager

This service manages OAuth connections for Composio integrations, implementing
the authentication flow described in composio_auth.md.

Key responsibilities:
1. Initiate OAuth connections using entity.initiate_connection()
2. <PERSON>le redirect URLs for user authentication
3. Wait for connection activation using wait_until_active()
4. Store connection metadata and status
5. Generate MCP URLs with connected_account_id
"""

import asyncio
from typing import Dict, Any, Optional, List
from dataclasses import dataclass
from datetime import datetime, timedelta
from utils.logger import logger
from utils.config import config
from supabase import create_client, Client
from services.composio_entity_manager import composio_entity_manager, EntityInfo
from constants.composio_mcp_constants import (
    get_composio_integration_id,
    get_composio_mcp_url_with_connected_account,
    get_composio_app_config,
)

try:
    from composio import ComposioToolSet
    from composio.exceptions import SDKTimeoutError

    Entity = None  # Entity objects are obtained from toolset.get_entity()
except ImportError:
    logger.warning(
        "composio-core not installed. Install with: pip install composio-core"
    )
    ComposioToolSet = None
    SDKTimeoutError = None
    Entity = None


@dataclass
class ConnectionInfo:
    """Information about a Composio OAuth connection"""

    connection_id: Optional[str]
    user_id: str
    entity_id: str
    app_key: str
    integration_id: str
    status: str  # pending, active, failed, expired
    redirect_url: Optional[str]
    connected_account_id: Optional[str]
    mcp_url: Optional[str]
    expires_at: Optional[datetime]
    metadata: Optional[Dict[str, Any]] = None


@dataclass
class ConnectionResult:
    """Result of connection initiation"""

    success: bool
    connection_info: Optional[ConnectionInfo] = None
    error: Optional[str] = None


class ComposioOAuthManager:
    """
    Service for managing Composio OAuth connections.

    This implements the OAuth flow from composio_auth.md:
    1. Get entity for user
    2. Initiate connection with integration_id
    3. Get redirect URL for user authentication
    4. Wait for connection activation
    5. Generate MCP URL with connected_account_id
    """

    def __init__(self):
        """Initialize the OAuth manager with Composio SDK and database connection."""
        if not ComposioToolSet:
            logger.warning(
                "composio-core package not available. Some features will be disabled."
            )
            self.toolset = None
        else:
            # Initialize Composio SDK
            self.composio_api_key = config.COMPOSIO_API_KEY
            if not self.composio_api_key:
                logger.warning(
                    "COMPOSIO_API_KEY not set. Composio features will be disabled."
                )
                self.toolset = None
            else:
                self.toolset = ComposioToolSet(api_key=self.composio_api_key)

        # Initialize Supabase client
        self.supabase: Client = create_client(
            config.SUPABASE_URL, config.SUPABASE_SERVICE_ROLE_KEY
        )

        logger.info("Composio OAuth Manager initialized successfully")

    async def initiate_connection(self, user_id: str, app_key: str) -> ConnectionResult:
        """
        Initiate OAuth connection for a user and app.

        This implements the flow from composio_auth.md:
        1. Get entity for user
        2. Use toolset.initiate_connection(integration_id, entity_id)
        3. Return redirect URL for user authentication

        Args:
            user_id: The user's UUID
            app_key: The app key (e.g., "gmail", "slack", "notion")

        Returns:
            ConnectionResult with redirect URL or error
        """
        try:
            logger.info(
                f"Initiating OAuth connection for user {user_id}, app {app_key}"
            )

            # Step 1: Get integration ID for the app
            integration_id = get_composio_integration_id(app_key)
            if not integration_id:
                return ConnectionResult(
                    success=False, error=f"No integration ID found for app {app_key}"
                )

            # Step 2: Get or create entity for user
            entity_info = await composio_entity_manager.get_or_create_entity(user_id)
            if not entity_info:
                return ConnectionResult(
                    success=False, error=f"Failed to get entity for user {user_id}"
                )

            # Step 3: Get Composio entity object
            entity = composio_entity_manager.get_composio_entity(entity_info.entity_id)
            if not entity:
                return ConnectionResult(
                    success=False,
                    error=f"Failed to get Composio entity {entity_info.entity_id}",
                )

            # Step 4: Skip checking existing connections - use in-memory flow only
            # We'll check OAuth completion status using Composio SDK directly

            # Step 5: Initiate connection using Composio SDK
            logger.info(f"Initiating connection with integration_id {integration_id}")

            # Check if toolset is available
            if not self.toolset:
                return ConnectionResult(
                    success=False, error="Composio SDK not properly initialized"
                )

            # This is the key call from composio_auth.md:
            # Use toolset.initiate_connection() instead of entity.initiate_connection()
            connection_request = self.toolset.initiate_connection(
                integration_id=integration_id,
                entity_id=entity_info.entity_id,
                redirect_url="https://atlasagents.ai/dashboard",
            )

            # Extract redirect URL
            redirect_url = connection_request.redirectUrl
            if not redirect_url:
                return ConnectionResult(
                    success=False, error="No redirect URL returned from Composio"
                )

            # Step 6: Store connection_request in memory only (no database)
            connection_info = ConnectionInfo(
                connection_id=None,  # Will be set after activation
                user_id=user_id,
                entity_id=entity_info.entity_id,
                app_key=app_key,
                integration_id=integration_id,
                status="pending",
                redirect_url=redirect_url,
                connected_account_id=None,  # Will be set after activation
                mcp_url=None,  # Will be generated after activation
                expires_at=datetime.now() + timedelta(minutes=30),  # 30 min expiry
                metadata={
                    "connection_request_id": getattr(connection_request, "id", None),
                    "initiated_at": datetime.now().isoformat(),
                },
            )

            # Store ONLY connection_request object in memory for wait_until_active
            if not hasattr(self, "_connection_requests"):
                self._connection_requests = {}

            connection_request_id = getattr(connection_request, "id", None)
            if connection_request_id:
                self._connection_requests[connection_request_id] = connection_request
                # Also store connection_info for easy retrieval
                cache_key = f"{user_id}_{app_key}"
                self._connection_requests[cache_key] = {
                    "connection_info": connection_info,
                    "connection_request": connection_request
                }
                logger.info(
                    f"Stored connection_request {connection_request_id} in memory for wait_until_active"
                )

            logger.info(
                f"Successfully initiated connection for {app_key}, redirect URL: {redirect_url}"
            )
            return ConnectionResult(success=True, connection_info=connection_info)

        except Exception as e:
            logger.error(
                f"Error initiating connection for user {user_id}, app {app_key}: {e}"
            )
            return ConnectionResult(success=False, error=str(e))

    async def wait_for_connection_activation(
        self, user_id: str, app_key: str, timeout: int = 180
    ) -> ConnectionResult:
        """
        Wait for OAuth connection to become active.

        This implements the wait_until_active pattern from composio_auth.md:
        active_connection = connection_request.wait_until_active(client=toolset.client, timeout=180)

        Args:
            user_id: The user's UUID
            app_key: The app key
            timeout: Timeout in seconds (default 180 = 3 minutes)

        Returns:
            ConnectionResult with active connection info or error
        """
        try:
            logger.info(
                f"Waiting for connection activation for user {user_id}, app {app_key}"
            )

            # Check for existing active connections using Composio SDK
            entity_info = await composio_entity_manager.get_or_create_entity(user_id)
            if not entity_info:
                return ConnectionResult(
                    success=False, error=f"Failed to get entity for user {user_id}"
                )

            try:
                # Get all connected accounts for this entity
                # Try different method signatures based on SDK version
                logger.info(f"🔍 Attempting to get connected accounts for entity: {entity_info.entity_id}")
                logger.info(f"🔍 Toolset type: {type(self.toolset)}")
                
                # FIXED: Use the correct SDK method signature
                # Based on debug results, entity_id parameter is not supported in current SDK version
                connections = None
                logger.info(f"🔍 Getting ALL connected accounts (SDK doesn't support entity_id filter)")
                
                try:
                    connections = self.toolset.get_connected_accounts()
                    logger.info(f"🔍 SUCCESS: Retrieved {len(connections)} total connections")
                    
                    # CRITICAL DEBUG: Log the exact state of what we got
                    if len(connections) == 0:
                        logger.warning(f"🔍 PROBLEM: SDK returned 0 connections despite OAuth completion")
                        logger.warning(f"🔍 This could indicate:")
                        logger.warning(f"🔍   1. Connection not yet active in Composio (timing issue)")
                        logger.warning(f"🔍   2. Connection created under different entity")
                        logger.warning(f"🔍   3. API key scope/permission issue")
                        logger.warning(f"🔍   4. Connection failed but OAuth appeared successful")
                        
                        # RETRY LOGIC: Try again after short delay
                        logger.info(f"🔍 RETRYING: Will wait 3 seconds and try again...")
                        import asyncio
                        await asyncio.sleep(3)
                        
                        connections_retry = self.toolset.get_connected_accounts()
                        logger.info(f"🔍 RETRY RESULT: {len(connections_retry)} connections found")
                        if len(connections_retry) > 0:
                            connections = connections_retry
                            logger.info(f"🔍 ✅ SUCCESS on retry!")
                        else:
                            logger.error(f"🔍 ❌ Still 0 connections after retry")
                            
                except Exception as e:
                    logger.error(f"🔍 FAILED to get connected accounts: {e}")
                    logger.error(f"🔍 Exception type: {type(e)}")
                    import traceback
                    logger.error(f"🔍 Full traceback: {traceback.format_exc()}")
                    raise
                        
                if connections is None:
                    logger.error(f"🔍 Both connection methods failed!")
                    raise Exception("Could not retrieve connected accounts")
                    
                logger.info(f"🔍 Raw connections response type: {type(connections)}")
                logger.info(f"🔍 Raw connections response: {connections}")
                logger.info(f"Found {len(connections)} active connections for entity {entity_info.entity_id}")
                
                # Look for active connection matching our app
                integration_id = get_composio_integration_id(app_key)
                active_connection = None
                
                # Log ALL connections for debugging
                if len(connections) > 0:
                    logger.info(f"🔍 ALL CONNECTIONS FOR ENTITY {entity_info.entity_id}:")
                    for i, conn in enumerate(connections):
                        logger.info(f"🔍 Connection {i+1}:")
                        logger.info(f"🔍   Raw connection object: {conn}")
                        logger.info(f"🔍   Connection type: {type(conn)}")
                        logger.info(f"🔍   Connection dir: {dir(conn)}")
                        
                        # Try to access properties safely
                        try:
                            app_name = getattr(conn, 'appName', None) or getattr(conn, 'app_name', None)
                            conn_id = getattr(conn, 'id', None)
                            status = getattr(conn, 'status', None)
                            integration_id = getattr(conn, 'integrationId', None) or getattr(conn, 'integration_id', None)
                            
                            logger.info(f"🔍   App={app_name}, ID={conn_id}, Status={status}")
                            logger.info(f"🔍   Integration ID: {integration_id}")
                            
                            if app_name and app_name.lower() == app_key.lower():
                                logger.info(f"🔍   ✅ MATCHES APP {app_key}!")
                                if status == "ACTIVE":
                                    active_connection = conn
                                    logger.info(f"🔍   ✅ CONNECTION IS ACTIVE!")
                                else:
                                    logger.info(f"🔍   ⚠️ Connection status: {status} (not ACTIVE)")
                        except Exception as prop_error:
                            logger.error(f"🔍   Error accessing connection properties: {prop_error}")
                else:
                    logger.info(f"🔍 No connections found for entity {entity_info.entity_id}")
                    
                # Also try getting connections for ALL entities to see if entity ID is the issue
                logger.info(f"🔍 DEBUGGING: Trying to get ALL connections across all entities...")
                try:
                    all_connections = self.toolset.get_connected_accounts()
                    logger.info(f"🔍 Found {len(all_connections)} total connections across all entities")
                    if len(all_connections) > 0:
                        for i, conn in enumerate(all_connections):
                            try:
                                app_name = getattr(conn, 'appName', None) or getattr(conn, 'app_name', None)
                                conn_id = getattr(conn, 'id', None) 
                                entity_id = getattr(conn, 'entityId', None) or getattr(conn, 'entity_id', None)
                                logger.info(f"🔍 Global connection {i+1}: App={app_name}, ID={conn_id}, Entity={entity_id}")
                            except Exception as e:
                                logger.error(f"🔍 Error reading global connection {i+1}: {e}")
                except Exception as e:
                    logger.error(f"🔍 Failed to get global connections: {e}")
                
                if active_connection:
                    logger.info(f"✅ Found existing active connection for {app_key}: {active_connection.id}")
                    
                    # Generate MCP URL with connected_account_id
                    connected_account_id = active_connection.id
                    mcp_url = get_composio_mcp_url_with_connected_account(
                        app_key, connected_account_id
                    )
                    
                    # Store the final MCP URL in agents table
                    try:
                        from services.composio_integration import composio_mcp_service
                        session_uuid = f"oauth_{user_id}_{app_key}_{connected_account_id[:8]}"
                        await composio_mcp_service._store_mcp_connection(
                            user_id, app_key, mcp_url, session_uuid
                        )
                        logger.info(f"✅ Stored MCP URL in agents table for {app_key}")
                    except Exception as store_error:
                        logger.warning(f"Failed to store MCP URL: {store_error}")
                    
                    # Return success with active connection
                    connection_info = ConnectionInfo(
                        connection_id=active_connection.id,
                        user_id=user_id,
                        entity_id=entity_info.entity_id,
                        app_key=app_key,
                        integration_id=integration_id,
                        status="active",
                        redirect_url=None,
                        connected_account_id=connected_account_id,
                        mcp_url=mcp_url,
                        expires_at=None,
                        metadata={"source": "existing_connection"},
                    )
                    
                    return ConnectionResult(success=True, connection_info=connection_info)
                    
            except Exception as e:
                logger.error(f"🔍 CRITICAL ERROR checking existing connections: {e}")
                logger.error(f"🔍 Exception type: {type(e)}")
                logger.error(f"🔍 Exception details: {str(e)}")
                import traceback
                logger.error(f"🔍 Full traceback: {traceback.format_exc()}")
                # Continue to check for pending connections

            # No active connection found - check for pending connections in memory cache
            cache_key = f"{user_id}_{app_key}"
            if hasattr(self, "_connection_requests") and cache_key in self._connection_requests:
                cached_data = self._connection_requests[cache_key]
                connection_info = cached_data["connection_info"]
                connection_request = cached_data["connection_request"]
                logger.info(f"Found pending connection in cache for {user_id}_{app_key}")
            else:
                return ConnectionResult(
                    success=False, error="No active or pending connection found. Please initiate connection first."
                )

            # Get entity (we already have connection_request from cache)
            entity = composio_entity_manager.get_composio_entity(
                connection_info.entity_id
            )
            if not entity:
                return ConnectionResult(
                    success=False,
                    error=f"Failed to get entity {connection_info.entity_id}",
                )

            # We already have connection_request from cache, no need to retrieve again
            logger.info(f"Using cached connection_request for OAuth completion")

            # Use the correct wait_until_active from Composio SDK as per documentation
            logger.info(
                f"Waiting for user authorization and connection activation (timeout: {timeout}s)"
            )
            logger.info(
                "Using connection_request.wait_until_active() as per Composio documentation"
            )

            try:
                # Use the correct Composio SDK method as per documentation
                # This is the proper way to wait for OAuth completion
                active_connection = connection_request.wait_until_active(
                    client=self.toolset.client,  # Pass the Composio client instance
                    timeout=float(
                        timeout
                    ),  # Wait up to specified timeout (adjust as needed)
                )

                # Extract connected_account_id from the active connection
                # According to Composio docs: active_connection.id is the connected_account_id
                connected_account_id = active_connection.id
                logger.info(
                    f"Success! Connection is ACTIVE. Connected Account ID: {connected_account_id}"
                )

                # Clean up the in-memory cache
                cache_key = f"{user_id}_{app_key}"
                if hasattr(self, "_connection_requests"):
                    if cache_key in self._connection_requests:
                        del self._connection_requests[cache_key]
                    connection_request_id = connection_info.metadata.get("connection_request_id")
                    if connection_request_id and connection_request_id in self._connection_requests:
                        del self._connection_requests[connection_request_id]
                    logger.info(f"Cleaned up connection cache for {user_id}_{app_key}")

                # Update connection info with active status and connected_account_id
                connection_info.status = "active"
                connection_info.connection_id = getattr(
                    active_connection, "connectionId", connected_account_id
                )
                connection_info.connected_account_id = connected_account_id

                # Generate MCP URL with connected_account_id
                mcp_url = get_composio_mcp_url_with_connected_account(
                    app_key, connected_account_id
                )
                connection_info.mcp_url = mcp_url

                # Skip database update - using agents table only

                # 🔥 CRITICAL: Store MCP URL in agents table using existing service
                # Use the pre-existing composio_integration.py service for agent storage
                try:
                    from services.composio_integration import composio_mcp_service

                    # Generate session UUID for storage compatibility
                    session_uuid = (
                        f"oauth_{user_id}_{app_key}_{connected_account_id[:8]}"
                    )

                    agent_storage_success = (
                        await composio_mcp_service._store_mcp_connection(
                            user_id, app_key, mcp_url, session_uuid
                        )
                    )
                    if agent_storage_success:
                        logger.info(
                            f"✅ Successfully stored MCP URL in agents table for {app_key}"
                        )
                    else:
                        logger.warning(
                            f"⚠️ Failed to store MCP URL in agents table for {app_key}"
                        )
                except Exception as agent_error:
                    logger.error(
                        f"❌ Error storing MCP URL in agents table: {agent_error}"
                    )
                    # Don't fail the entire flow if agent storage fails

                logger.info(
                    f"Connection activated for user {user_id}, app {app_key}, MCP URL: {mcp_url}"
                )
                return ConnectionResult(success=True, connection_info=connection_info)

            except Exception as sdk_error:
                # Handle SDKTimeoutError and other exceptions
                if SDKTimeoutError and isinstance(sdk_error, SDKTimeoutError):
                    logger.warning(
                        f"OAuth completion timeout for user {user_id}, app {app_key}"
                    )
                    error_msg = "Connection did not become active within timeout period"
                else:
                    logger.error(f"OAuth completion error: {sdk_error}")
                    error_msg = f"OAuth completion failed: {str(sdk_error)}"

                # Clean up the in-memory cache on error
                cache_key = f"{user_id}_{app_key}"
                if hasattr(self, "_connection_requests"):
                    if cache_key in self._connection_requests:
                        del self._connection_requests[cache_key]
                    connection_request_id = connection_info.metadata.get("connection_request_id")
                    if connection_request_id and connection_request_id in self._connection_requests:
                        del self._connection_requests[connection_request_id]
                    logger.info(f"Cleaned up connection cache after error for {user_id}_{app_key}")

                # Skip database update on error - using in-memory only

                return ConnectionResult(success=False, error=error_msg)

        except Exception as e:
            logger.error(f"Error waiting for connection activation: {e}")
            return ConnectionResult(success=False, error=str(e))

    async def get_connection(
        self, user_id: str, app_key: str
    ) -> Optional[ConnectionInfo]:
        """
        Get connection information for user and app.

        Args:
            user_id: The user's UUID
            app_key: The app key

        Returns:
            ConnectionInfo if found, None otherwise
        """
        try:
            return await self._get_stored_connection(user_id, app_key)
        except Exception as e:
            logger.error(
                f"Error getting connection for user {user_id}, app {app_key}: {e}"
            )
            return None

    async def list_user_connections(self, user_id: str) -> List[ConnectionInfo]:
        """
        List all connections for a user.

        Args:
            user_id: The user's UUID

        Returns:
            List of ConnectionInfo objects
        """
        try:
            result = (
                self.supabase.table("composio_connections")
                .select("*")
                .eq("user_id", user_id)
                .order("created_at", desc=True)
                .execute()
            )

            connections = []
            for conn_data in result.data:
                connections.append(self._connection_from_db(conn_data))

            return connections

        except Exception as e:
            logger.error(f"Error listing connections for user {user_id}: {e}")
            return []

    async def delete_connection(self, user_id: str, app_key: str) -> bool:
        """
        Delete a connection for user and app.

        Args:
            user_id: The user's UUID
            app_key: The app key

        Returns:
            True if successful, False otherwise
        """
        try:
            result = (
                self.supabase.table("composio_connections")
                .delete()
                .eq("user_id", user_id)
                .eq("app_key", app_key)
                .execute()
            )

            if result.data:
                logger.info(
                    f"Successfully deleted connection for user {user_id}, app {app_key}"
                )
                return True
            else:
                logger.warning(
                    f"No connection found to delete for user {user_id}, app {app_key}"
                )
                return False

        except Exception as e:
            logger.error(
                f"Error deleting connection for user {user_id}, app {app_key}: {e}"
            )
            return False

    async def _get_stored_connection(
        self, user_id: str, app_key: str
    ) -> Optional[ConnectionInfo]:
        """Get connection from database."""
        try:
            result = (
                self.supabase.table("composio_connections")
                .select("*")
                .eq("user_id", user_id)
                .eq("app_key", app_key)
                .execute()
            )

            if result.data:
                return self._connection_from_db(result.data[0])

            return None

        except Exception as e:
            logger.error(f"Error getting stored connection: {e}")
            return None

    async def _store_connection(self, connection_info: ConnectionInfo) -> bool:
        """Store connection in database."""
        try:
            result = (
                self.supabase.table("composio_connections")
                .insert(
                    {
                        "entity_id": connection_info.entity_id,
                        "user_id": connection_info.user_id,
                        "app_key": connection_info.app_key,
                        "integration_id": connection_info.integration_id,
                        "connection_id": connection_info.connection_id,
                        "connected_account_id": connection_info.connected_account_id,
                        "status": connection_info.status,
                        "redirect_url": connection_info.redirect_url,
                        "expires_at": (
                            connection_info.expires_at.isoformat()
                            if connection_info.expires_at
                            else None
                        ),
                        "metadata": connection_info.metadata or {},
                    }
                )
                .execute()
            )

            if result.data:
                logger.info(
                    f"Successfully stored connection for user {connection_info.user_id}, app {connection_info.app_key}"
                )
                return True
            else:
                logger.error(f"Failed to store connection")
                return False

        except Exception as e:
            logger.error(f"Error storing connection: {e}")
            return False

    async def _update_connection(self, connection_info: ConnectionInfo) -> bool:
        """Update connection in database."""
        try:
            result = (
                self.supabase.table("composio_connections")
                .update(
                    {
                        "connection_id": connection_info.connection_id,
                        "connected_account_id": connection_info.connected_account_id,
                        "status": connection_info.status,
                        "metadata": connection_info.metadata or {},
                    }
                )
                .eq("user_id", connection_info.user_id)
                .eq("app_key", connection_info.app_key)
                .execute()
            )

            return bool(result.data)

        except Exception as e:
            logger.error(f"Error updating connection: {e}")
            return False

    def _connection_from_db(self, conn_data: Dict[str, Any]) -> ConnectionInfo:
        """Convert database row to ConnectionInfo object."""
        expires_at = None
        if conn_data.get("expires_at"):
            expires_at = datetime.fromisoformat(
                conn_data["expires_at"].replace("Z", "+00:00")
            )

        return ConnectionInfo(
            connection_id=conn_data.get("connection_id"),
            user_id=conn_data["user_id"],
            entity_id=conn_data["entity_id"],
            app_key=conn_data["app_key"],
            integration_id=conn_data["integration_id"],
            status=conn_data["status"],
            redirect_url=conn_data.get("redirect_url"),
            connected_account_id=conn_data.get("connected_account_id"),
            mcp_url=conn_data.get("mcp_url"),
            expires_at=expires_at,
            metadata=conn_data.get("metadata"),
        )

    async def check_connection_status(
        self, user_id: str, app_key: str
    ) -> Optional[str]:
        """
        Check the current status of a connection using Composio SDK.

        Args:
            user_id: The user's UUID
            app_key: The app key

        Returns:
            Connection status string or None if not found
        """
        try:
            logger.info(f"🔍 CHECK_CONNECTION_STATUS for user {user_id}, app {app_key}")
            
            # Get entity for user
            entity_info = await composio_entity_manager.get_or_create_entity(user_id)
            if not entity_info:
                logger.warning(f"🔍 No entity info for user {user_id}")
                return None
                
            logger.info(f"🔍 Using entity_id: {entity_info.entity_id}")

            # Check for existing connections using Composio SDK
            # FIXED: Don't use entity_id parameter as it's not supported
            connections = None
            try:
                logger.info(f"🔍 Getting all connected accounts for status check")
                connections = self.toolset.get_connected_accounts()
                logger.info(f"🔍 Retrieved {len(connections)} total connections for status check")
            except Exception as e:
                logger.error(f"🔍 Failed to get connections for status check: {e}")
                return None
                
            if not connections:
                logger.info(f"🔍 No connections returned from SDK")
                return None
            
            logger.info(f"🔍 Retrieved {len(connections)} connections")
            
            for i, conn in enumerate(connections):
                try:
                    app_name = getattr(conn, 'appName', None) or getattr(conn, 'app_name', None)
                    status = getattr(conn, 'status', None)
                    logger.info(f"🔍 Connection {i+1}: app={app_name}, status={status}")
                    
                    if app_name and app_name.lower() == app_key.lower():
                        logger.info(f"🔍 FOUND matching connection for {app_key}: {status}")
                        return status
                except Exception as prop_error:
                    logger.error(f"🔍 Error reading connection {i+1}: {prop_error}")
            
            logger.info(f"🔍 No matching connection found for {app_key}")
            return None
        except Exception as e:
            logger.error(f"🔍 Error checking connection status: {e}")
            import traceback
            logger.error(f"🔍 Full traceback: {traceback.format_exc()}")
            return None

    async def refresh_connection_status(self, user_id: str, app_key: str) -> bool:
        """
        Refresh connection status by checking with Composio API.

        This method would typically query the Composio API to get the latest
        connection status and update our local database.

        Args:
            user_id: The user's UUID
            app_key: The app key

        Returns:
            True if refresh was successful, False otherwise
        """
        try:
            logger.info(
                f"Refreshing connection status for user {user_id}, app {app_key}"
            )

            connection_info = await self._get_stored_connection(user_id, app_key)
            if not connection_info:
                logger.warning(
                    f"No connection found to refresh for user {user_id}, app {app_key}"
                )
                return False

            # In a real implementation, you would:
            # 1. Query Composio API for connection status
            # 2. Update local database with latest status
            # 3. Handle token refresh if needed

            # For now, we'll just log that refresh was attempted
            logger.info(f"Connection status refresh completed for {app_key}")
            return True

        except Exception as e:
            logger.error(f"Error refreshing connection status: {e}")
            return False

    async def expire_connection(self, user_id: str, app_key: str) -> bool:
        """
        Mark a connection as expired.

        Args:
            user_id: The user's UUID
            app_key: The app key

        Returns:
            True if successful, False otherwise
        """
        try:
            result = (
                self.supabase.table("composio_connections")
                .update({"status": "expired"})
                .eq("user_id", user_id)
                .eq("app_key", app_key)
                .execute()
            )

            if result.data:
                logger.info(
                    f"Successfully expired connection for user {user_id}, app {app_key}"
                )
                return True
            else:
                logger.warning(
                    f"No connection found to expire for user {user_id}, app {app_key}"
                )
                return False

        except Exception as e:
            logger.error(f"Error expiring connection: {e}")
            return False

    async def reactivate_connection(
        self, user_id: str, app_key: str
    ) -> ConnectionResult:
        """
        Reactivate an expired or failed connection.

        This will initiate a new OAuth flow for the connection.

        Args:
            user_id: The user's UUID
            app_key: The app key

        Returns:
            ConnectionResult with new redirect URL or error
        """
        try:
            logger.info(f"Reactivating connection for user {user_id}, app {app_key}")

            # Delete existing connection
            await self.delete_connection(user_id, app_key)

            # Initiate new connection
            return await self.initiate_connection(user_id, app_key)

        except Exception as e:
            logger.error(f"Error reactivating connection: {e}")
            return ConnectionResult(success=False, error=str(e))


# Create singleton instance
composio_oauth_manager = ComposioOAuthManager()
