BEGIN;

-- Create table for storing Composio entity mappings
CREATE TABLE IF NOT EXISTS public.composio_entities (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    entity_id TEXT NOT NULL UNIQUE, -- Composio entity ID
    user_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
    is_active BOOLEAN DEFAULT true,
    metadata JSONB DEFAULT '{}'::jsonb,
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW(),

    -- Unique constraint to ensure one active entity per user
    UNIQUE(user_id, is_active) DEFERRABLE INITIALLY DEFERRED
);

-- Add indexes for performance
CREATE INDEX IF NOT EXISTS idx_composio_entities_user_id ON public.composio_entities(user_id);
CREATE INDEX IF NOT EXISTS idx_composio_entities_entity_id ON public.composio_entities(entity_id);
CREATE INDEX IF NOT EXISTS idx_composio_entities_active ON public.composio_entities(is_active);
CREATE INDEX IF NOT EXISTS idx_composio_entities_created_at ON public.composio_entities(created_at);

-- Create function to update updated_at timestamp
CREATE OR REPLACE FUNCTION update_composio_entities_updated_at()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Create trigger for updated_at
DROP TRIGGER IF EXISTS trigger_composio_entities_updated_at ON public.composio_entities;
CREATE TRIGGER trigger_composio_entities_updated_at
    BEFORE UPDATE ON public.composio_entities
    FOR EACH ROW
    EXECUTE FUNCTION update_composio_entities_updated_at();

-- Enable RLS on composio_entities table
ALTER TABLE public.composio_entities ENABLE ROW LEVEL SECURITY;

-- Drop existing policies if they exist to avoid conflicts
DROP POLICY IF EXISTS composio_entities_select_own ON public.composio_entities;
DROP POLICY IF EXISTS composio_entities_insert_own ON public.composio_entities;
DROP POLICY IF EXISTS composio_entities_update_own ON public.composio_entities;
DROP POLICY IF EXISTS composio_entities_delete_own ON public.composio_entities;

-- Policy for users to see their own entities
CREATE POLICY composio_entities_select_own ON public.composio_entities
    FOR SELECT
    USING (auth.uid() = user_id);

-- Policy for users to insert their own entities
CREATE POLICY composio_entities_insert_own ON public.composio_entities
    FOR INSERT
    WITH CHECK (auth.uid() = user_id);

-- Policy for users to update their own entities
CREATE POLICY composio_entities_update_own ON public.composio_entities
    FOR UPDATE
    USING (auth.uid() = user_id);

-- Policy for users to delete their own entities
CREATE POLICY composio_entities_delete_own ON public.composio_entities
    FOR DELETE
    USING (auth.uid() = user_id);

-- Create table for storing Composio connections
CREATE TABLE IF NOT EXISTS public.composio_connections (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    entity_id TEXT NOT NULL REFERENCES public.composio_entities(entity_id) ON DELETE CASCADE,
    user_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
    app_key TEXT NOT NULL, -- e.g., "gmail", "slack", "notion"
    integration_id TEXT NOT NULL, -- Composio integration ID
    connection_id TEXT, -- Composio connection ID (set after successful OAuth)
    connected_account_id TEXT, -- Composio connected account ID (for MCP URL generation)
    status TEXT DEFAULT 'pending', -- pending, active, failed, expired
    redirect_url TEXT, -- OAuth redirect URL
    expires_at TIMESTAMPTZ,
    metadata JSONB DEFAULT '{}'::jsonb,
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW(),

    -- Unique constraint to prevent duplicate connections per user per app
    UNIQUE(user_id, app_key)
);

-- Add indexes for performance on connections table
CREATE INDEX IF NOT EXISTS idx_composio_connections_entity_id ON public.composio_connections(entity_id);
CREATE INDEX IF NOT EXISTS idx_composio_connections_user_id ON public.composio_connections(user_id);
CREATE INDEX IF NOT EXISTS idx_composio_connections_app_key ON public.composio_connections(app_key);
CREATE INDEX IF NOT EXISTS idx_composio_connections_status ON public.composio_connections(status);
CREATE INDEX IF NOT EXISTS idx_composio_connections_created_at ON public.composio_connections(created_at);

-- Create trigger for updated_at on connections table
DROP TRIGGER IF EXISTS trigger_composio_connections_updated_at ON public.composio_connections;
CREATE TRIGGER trigger_composio_connections_updated_at
    BEFORE UPDATE ON public.composio_connections
    FOR EACH ROW
    EXECUTE FUNCTION update_composio_entities_updated_at();

-- Enable RLS on composio_connections table
ALTER TABLE public.composio_connections ENABLE ROW LEVEL SECURITY;

-- Drop existing policies if they exist to avoid conflicts
DROP POLICY IF EXISTS composio_connections_select_own ON public.composio_connections;
DROP POLICY IF EXISTS composio_connections_insert_own ON public.composio_connections;
DROP POLICY IF EXISTS composio_connections_update_own ON public.composio_connections;
DROP POLICY IF EXISTS composio_connections_delete_own ON public.composio_connections;

-- Policy for users to see their own connections
CREATE POLICY composio_connections_select_own ON public.composio_connections
    FOR SELECT
    USING (auth.uid() = user_id);

-- Policy for users to insert their own connections
CREATE POLICY composio_connections_insert_own ON public.composio_connections
    FOR INSERT
    WITH CHECK (auth.uid() = user_id);

-- Policy for users to update their own connections
CREATE POLICY composio_connections_update_own ON public.composio_connections
    FOR UPDATE
    USING (auth.uid() = user_id);

-- Policy for users to delete their own connections
CREATE POLICY composio_connections_delete_own ON public.composio_connections
    FOR DELETE
    USING (auth.uid() = user_id);

COMMIT;
