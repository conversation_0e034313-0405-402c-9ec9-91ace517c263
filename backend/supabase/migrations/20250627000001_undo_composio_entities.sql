BEGIN;

-- Undo Migration: Remove Composio Entities and Connections Tables
-- Description: Remove the unnecessary composio_entities and composio_connections tables
-- since we're using the existing agents.custom_mcps column for MCP storage

-- Drop the composio_connections table and all its dependencies
DROP TABLE IF EXISTS public.composio_connections CASCADE;

-- Drop the composio_entities table and all its dependencies  
DROP TABLE IF EXISTS public.composio_entities CASCADE;

-- Drop the shared trigger function if it exists and is no longer needed
-- (Only drop if no other tables are using it)
DROP FUNCTION IF EXISTS update_composio_entities_updated_at() CASCADE;

-- Add comment to clarify the storage approach
COMMENT ON COLUMN agents.custom_mcps IS 'Stores custom MCP server configurations including Composio MCPs (stored as HTTP type). Composio MCPs use connected_account_id in URLs after OAuth completion. Format: [{"name": "Gmail", "type": "http", "config": {"url": "https://mcp.composio.dev/composio/server/{server_id}/mcp?connected_account_id={account_id}"}, "enabledTools": [...]}]';

COMMIT;
